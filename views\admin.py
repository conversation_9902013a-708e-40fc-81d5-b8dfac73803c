from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from functools import wraps
from models import db
from models.user import User
from models.product import Product, Category, AttributeGroup, Attribute
from models.order import Order, OrderItem, ProductAttribute, QuantityDiscount
from models.system import SystemConfig
from forms.admin import CategoryForm, AttributeGroupForm, AttributeForm, ProductForm, QuantityDiscountForm
from utils.file_upload import save_uploaded_file, delete_file
from utils.logger import admin_logger
from datetime import datetime, timedelta

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('需要管理员权限', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/')
@login_required
@admin_required
def index():
    """管理后台首页"""
    # 统计数据
    stats = {
        'total_users': User.query.count(),
        'total_products': Product.query.count(),
        'total_orders': Order.query.count(),
        'total_categories': Category.query.count()
    }
    
    # 最近订单
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
    
    # 最新商品
    recent_products = Product.query.filter_by(is_active=True).order_by(Product.id.desc()).limit(5).all()
    
    return render_template('admin/index.html', 
                         stats=stats,
                         recent_orders=recent_orders,
                         recent_products=recent_products)

# 分类管理
@admin_bp.route('/categories')
@login_required
@admin_required
def categories():
    """分类列表"""
    page = request.args.get('page', 1, type=int)
    categories = Category.query.order_by(Category.sort_order, Category.id).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/categories.html', categories=categories)

@admin_bp.route('/categories/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_category():
    """添加分类"""
    form = CategoryForm()
    if form.validate_on_submit():
        category = Category(
            name=form.name.data,
            description=form.description.data,
            parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
            sort_order=form.sort_order.data,
            is_active=form.is_active.data
        )

        try:
            db.session.add(category)
            db.session.commit()
            flash('分类添加成功', 'success')
            return redirect(url_for('admin.categories'))
        except Exception as e:
            db.session.rollback()
            flash('添加失败，请重试', 'error')
    
    return render_template('admin/category_form.html', form=form, title='添加分类')

@admin_bp.route('/categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_category(id):
    """编辑分类"""
    category = Category.query.get_or_404(id)
    form = CategoryForm(obj=category)

    if form.validate_on_submit():
        old_data = {'name': category.name}

        category.name = form.name.data
        category.description = form.description.data
        category.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
        category.sort_order = form.sort_order.data
        category.is_active = form.is_active.data

        try:
            db.session.commit()
            flash('分类更新成功', 'success')
            return redirect(url_for('admin.categories'))
        except Exception as e:
            db.session.rollback()
            flash('更新失败，请重试', 'error')

    return render_template('admin/category_form.html', form=form, category=category, title='编辑分类')

@admin_bp.route('/categories/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_category(id):
    """删除分类"""
    category = Category.query.get_or_404(id)

    # 检查是否有子分类
    if category.children:
        return jsonify({
            'success': False,
            'message': '该分类下还有子分类，无法删除。请先删除子分类。'
        }), 400

    # 检查是否有商品
    if category.products.count() > 0:
        return jsonify({
            'success': False,
            'message': f'该分类下还有 {category.products.count()} 个商品，无法删除。请先删除或移动商品。'
        }), 400

    # 检查是否有属性组
    if category.attribute_groups.count() > 0:
        return jsonify({
            'success': False,
            'message': f'该分类下还有 {category.attribute_groups.count()} 个属性组，无法删除。请先删除属性组。'
        }), 400

    try:
        old_data = {'name': category.name}
        db.session.delete(category)
        db.session.commit()
        return jsonify({'success': True, 'message': '分类删除成功'})
    except Exception as e:
        db.session.rollback()
        print(f"删除分类失败: {str(e)}")  # 添加调试信息
        return jsonify({'success': False, 'message': f'删除失败：{str(e)}'}), 500

# 属性组管理
@admin_bp.route('/attribute-groups')
@login_required
@admin_required
def attribute_groups():
    """属性组列表"""
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category_id', type=int)

    # 获取所有分类用于筛选
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()

    # 默认不显示任何数据，只有选择分类后才显示
    if category_id:
        # 验证分类是否存在
        selected_category = Category.query.get(category_id)
        if not selected_category:
            flash('指定的分类不存在', 'error')
            return render_template('admin/attribute_groups.html',
                                 groups=None,
                                 categories=categories,
                                 selected_category=None,
                                 category_id=None,
                                 show_empty_state=True)

        # 查询该分类的属性组
        query = AttributeGroup.query.filter_by(category_id=category_id)
        groups = query.order_by(AttributeGroup.sort_order, AttributeGroup.id).paginate(
            page=page, per_page=20, error_out=False
        )
    else:
        # 没有选择分类时，不显示任何数据
        selected_category = None
        groups = None

    return render_template('admin/attribute_groups.html',
                         groups=groups,
                         categories=categories,
                         selected_category=selected_category,
                         category_id=category_id,
                         show_empty_state=(category_id is None))

@admin_bp.route('/attribute-groups/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_attribute_group(id):
    """编辑属性组"""
    group = AttributeGroup.query.get_or_404(id)
    form = AttributeGroupForm(obj=group)

    if form.validate_on_submit():
        try:
            old_data = {
                'name': group.name,
                'category_id': group.category_id,
                'is_active': group.is_active,
                'allow_custom_input': group.allow_custom_input
            }

            # 更新基本字段
            group.category_id = form.category_id.data
            group.name = form.name.data
            group.description = form.description.data
            group.display_type = form.display_type.data
            group.sort_order = form.sort_order.data
            group.is_active = form.is_active.data

            # 更新多选支持字段（暂时跳过，等待数据库迁移）
            try:
                group.is_multiple = form.is_multiple.data
                group.max_selections = form.max_selections.data if form.max_selections.data else None
            except Exception as e:
                print(f"多选字段更新跳过: {e}")
                # 暂时跳过多选字段的保存

            # 更新自定义输入字段
            group.allow_custom_input = form.allow_custom_input.data
            group.custom_input_type = form.custom_input_type.data
            group.custom_input_label = form.custom_input_label.data
            group.custom_input_placeholder = form.custom_input_placeholder.data
            group.custom_validation_pattern = form.custom_validation_pattern.data
            group.custom_validation_message = form.custom_validation_message.data
            group.custom_price_modifier_type = form.custom_price_modifier_type.data
            group.custom_price_modifier = form.custom_price_modifier.data
            group.custom_price_formula = form.custom_price_formula.data

            db.session.commit()
            flash('属性组更新成功', 'success')
            return redirect(url_for('admin.attribute_groups'))
        except Exception as e:
            db.session.rollback()
            flash('更新失败，请重试', 'error')

    return render_template('admin/attribute_group_form.html', form=form, group=group, title='编辑属性组')

@admin_bp.route('/attribute-groups/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_attribute_group(id):
    """删除属性组"""
    from flask import current_app
    
    group = AttributeGroup.query.get_or_404(id)
    
    current_app.logger.info(f"尝试删除属性组 - ID: {id}, 名称: {group.name}")

    # 检查是否有属性 - 使用count()方法进行准确检查
    attributes_count = group.attributes.count()
    current_app.logger.info(f"属性组 {group.name} 下的属性数量: {attributes_count}")
    
    if attributes_count > 0:
        # 获取属性列表用于调试
        attributes_list = group.attributes.all()
        attributes_info = [f"{attr.name} (ID: {attr.id})" for attr in attributes_list]
        
        current_app.logger.warning(f"属性组 {group.name} 下有 {attributes_count} 个属性，无法删除。属性列表: {attributes_info}")
        
        return jsonify({
            'success': False, 
            'message': f'该属性组下还有 {attributes_count} 个属性，无法删除。请先删除所有属性后再删除属性组。',
            'attributes_count': attributes_count,
            'attributes_info': attributes_info
        })

    try:
        current_app.logger.info(f"开始删除属性组: {group.name}")
        db.session.delete(group)
        db.session.commit()
        
        current_app.logger.info(f"属性组删除成功: {group.name}")
        return jsonify({'success': True, 'message': '属性组删除成功'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除属性组失败 - {group.name}: {str(e)}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@admin_bp.route('/attribute-groups/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_attribute_group():
    """添加大属性组"""
    # 获取预选的分类ID
    category_id = request.args.get('category_id', type=int)
    form = AttributeGroupForm()

    # 如果有预选的分类，设置默认值
    if category_id and request.method == 'GET':
        form.category_id.data = category_id

    if form.validate_on_submit():
        try:
            # 创建属性组数据（暂时跳过多选字段）
            group_data = {
                'name': form.name.data,
                'description': form.description.data,
                'display_type': form.display_type.data,
                'sort_order': form.sort_order.data,
                'is_active': form.is_active.data,
                # 自定义输入字段
                'allow_custom_input': form.allow_custom_input.data,
                'custom_input_type': form.custom_input_type.data,
                'custom_input_label': form.custom_input_label.data,
                'custom_input_placeholder': form.custom_input_placeholder.data,
                'custom_validation_pattern': form.custom_validation_pattern.data,
                'custom_validation_message': form.custom_validation_message.data,
                'custom_price_modifier_type': form.custom_price_modifier_type.data,
                'custom_price_modifier': form.custom_price_modifier.data,
                'custom_price_formula': form.custom_price_formula.data
            }

            # 如果category_id字段存在且有值，则添加
            if hasattr(form, 'category_id') and form.category_id.data and form.category_id.data != 0:
                group_data['category_id'] = form.category_id.data

            group = AttributeGroup(**group_data)

            db.session.add(group)
            db.session.commit()
            flash('属性组添加成功', 'success')
            return redirect(url_for('admin.attribute_groups'))
        except Exception as e:
            db.session.rollback()
            print(f"[错误] 添加属性组失败: {str(e)}")
            print(f"[错误] 表单数据: {form.data}")
            import traceback
            traceback.print_exc()
            flash(f'添加失败: {str(e)}', 'error')
    
    return render_template('admin/attribute_group_form.html', form=form, title='添加属性组')

# 属性管理
@admin_bp.route('/attributes')
@login_required
@admin_required
def attributes():
    """属性列表"""
    page = request.args.get('page', 1, type=int)
    group_id = request.args.get('group_id', type=int)

    query = Attribute.query
    current_group = None
    
    if group_id:
        query = query.filter_by(group_id=group_id)
        current_group = AttributeGroup.query.get(group_id)

    attributes = query.order_by(Attribute.group_id, Attribute.sort_order, Attribute.id).paginate(
        page=page, per_page=20, error_out=False
    )

    # 获取所有属性组用于筛选
    groups = AttributeGroup.query.filter_by(is_active=True).order_by(AttributeGroup.name).all()

    return render_template('admin/attributes.html',
                         attributes=attributes,
                         groups=groups,
                         current_group=current_group,
                         current_group_id=group_id)

@admin_bp.route('/attributes/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_attribute():
    """添加属性"""
    # 获取预选的属性组ID
    group_id = request.args.get('group_id', type=int)

    form = AttributeForm()

    # 如果有预选的属性组，设置默认值
    if group_id and request.method == 'GET':
        form.group_id.data = group_id

    # 调试信息：检查表单提交
    if request.method == 'POST':
        print(f"[调试] 添加属性表单提交:")
        print(f"  - 表单验证状态: {form.validate()}")
        print(f"  - 表单数据: {form.data}")
        print(f"  - 表单错误: {form.errors}")
        print(f"  - group_id参数: {group_id}")

    if form.validate_on_submit():
        # 处理公式计算相关字段
        price_formula = None
        is_quantity_based = False
        quantity_unit = '个'
        min_quantity = 1
        max_quantity = 9999

        # 根据计算方式设置字段值
        calculation_type = form.calculation_type.data
        if calculation_type == 'formula':
            # 公式计算模式
            is_quantity_based = form.is_quantity_based.data
            if is_quantity_based:
                price_formula = form.price_formula.data
                quantity_unit = form.quantity_unit.data or '个'
                min_quantity = form.min_quantity.data or 1
                max_quantity = form.max_quantity.data or 9999
            # 清空传统价格调整
            price_modifier = 0
            price_modifier_type = 'fixed'
        else:
            # 传统价格调整模式
            price_modifier = form.price_modifier.data
            price_modifier_type = calculation_type  # 使用选择的计算类型

        attribute = Attribute(
            group_id=form.group_id.data,
            name=form.name.data,
            value=form.value.data,
            price_modifier=price_modifier,
            price_modifier_type=price_modifier_type,
            price_formula=price_formula,
            is_quantity_based=is_quantity_based,
            quantity_unit=quantity_unit,
            min_quantity=min_quantity,
            max_quantity=max_quantity,
            sort_order=form.sort_order.data,
            is_active=form.is_active.data
        )

        try:
            db.session.add(attribute)
            db.session.commit()

            # 记录审计日志
            admin_logger.log_admin_action(
                operation='add_attribute',
                details=f'添加属性: {attribute.name} (值: {attribute.value})',
                additional_data={
                    'attribute_id': attribute.id,
                    'calculation_type': calculation_type,
                    'is_quantity_based': is_quantity_based,
                    'price_formula': price_formula if price_formula else None
                }
            )

            flash('属性添加成功', 'success')

            print(f"[调试] 属性添加成功，不跳转，清空表单继续添加")
            print(f"  - group_id: {group_id}")

            # 不跳转，清空表单以便继续添加
            form = AttributeForm()  # 重新创建表单实例
            # 如果有预选的属性组，保持选中状态
            if group_id:
                form.group_id.data = group_id

        except Exception as e:
            db.session.rollback()
            admin_logger.log_database_error(
                e,
                operation='add_attribute',
                additional_data={'form_data': form.data}
            )
            flash('添加失败，请重试', 'error')
            print(f"[错误] 添加属性失败: {str(e)}")
    else:
        # 表单验证失败时的处理
        if request.method == 'POST':
            print(f"[调试] 表单验证失败，错误详情: {form.errors}")
            if form.errors:
                for field, errors in form.errors.items():
                    for error in errors:
                        flash(f'{field}: {error}', 'error')

    # 获取所有属性组用于表单选择和模板使用
    groups = AttributeGroup.query.filter_by(is_active=True).order_by(AttributeGroup.name).all()

    return render_template('admin/attribute_form.html',
                         form=form,
                         groups=groups,
                         group_id=group_id,
                         title='添加属性')

@admin_bp.route('/attributes/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_attribute(id):
    """编辑属性"""
    attribute = Attribute.query.get_or_404(id)
    form = AttributeForm(obj=attribute)
    
    # 获取上下文参数
    from_group = request.args.get('from_group', type=int)
    locked = request.args.get('locked', type=bool)

    # 在GET请求时，根据现有数据设置calculation_type字段
    if request.method == 'GET':
        if attribute.is_quantity_based and attribute.price_formula:
            form.calculation_type.data = 'formula'
        elif attribute.price_modifier_type == 'percentage':
            form.calculation_type.data = 'percentage'
        else:
            form.calculation_type.data = 'fixed'

    if form.validate_on_submit():
        old_data = {
            'name': attribute.name,
            'value': attribute.value,
            'price_modifier': float(attribute.price_modifier),
            'price_modifier_type': attribute.price_modifier_type,
            'price_formula': attribute.price_formula,
            'is_quantity_based': attribute.is_quantity_based,
            'quantity_unit': attribute.quantity_unit,
            'is_active': attribute.is_active
        }

        # 处理公式计算相关字段
        calculation_type = form.calculation_type.data
        
        if calculation_type == 'formula':
            # 公式计算模式
            attribute.is_quantity_based = form.is_quantity_based.data
            if attribute.is_quantity_based:
                attribute.price_formula = form.price_formula.data
                attribute.quantity_unit = form.quantity_unit.data or '个'
                attribute.min_quantity = form.min_quantity.data or 1
                attribute.max_quantity = form.max_quantity.data or 9999
            else:
                # 如果未启用数量输入，清空公式相关字段
                attribute.price_formula = None
                attribute.quantity_unit = '个'
                attribute.min_quantity = 1
                attribute.max_quantity = 9999
            # 清空传统价格调整
            attribute.price_modifier = 0
            attribute.price_modifier_type = 'fixed'
        else:
            # 传统价格调整模式
            attribute.price_modifier = form.price_modifier.data
            attribute.price_modifier_type = calculation_type  # 使用选择的计算类型
            # 清空公式相关字段
            attribute.price_formula = None
            attribute.is_quantity_based = False
            attribute.quantity_unit = '个'
            attribute.min_quantity = 1
            attribute.max_quantity = 9999

        # 更新基本字段
        attribute.group_id = form.group_id.data
        attribute.name = form.name.data
        attribute.value = form.value.data
        attribute.sort_order = form.sort_order.data
        attribute.is_active = form.is_active.data

        try:
            db.session.commit()

            # 记录审计日志
            new_data = {
                'name': attribute.name,
                'value': attribute.value,
                'price_modifier': float(attribute.price_modifier),
                'price_modifier_type': attribute.price_modifier_type,
                'price_formula': attribute.price_formula,
                'is_quantity_based': attribute.is_quantity_based,
                'quantity_unit': attribute.quantity_unit,
                'is_active': attribute.is_active
            }
            
            admin_logger.log_admin_action(
                operation='edit_attribute',
                details=f'编辑属性: {attribute.name} (值: {attribute.value})',
                additional_data={
                    'attribute_id': attribute.id,
                    'calculation_type': calculation_type,
                    'old_data': old_data,
                    'new_data': new_data
                }
            )

            flash('属性更新成功', 'success')

            print(f"[调试] 属性更新成功，不跳转，保持在编辑页面")

            # 不跳转，保持在当前编辑页面
        except Exception as e:
            db.session.rollback()
            admin_logger.log_database_error(
                e,
                operation='edit_attribute',
                additional_data={
                    'attribute_id': id,
                    'form_data': form.data,
                    'old_data': old_data
                }
            )
            flash('更新失败，请重试', 'error')

    # 获取来源属性组信息
    source_group = AttributeGroup.query.get(from_group) if from_group else None

    return render_template('admin/attribute_form.html', 
                         form=form, 
                         attribute=attribute, 
                         title='编辑属性',
                         source_group=source_group,
                         from_group=from_group,
                         locked=locked)

@admin_bp.route('/attributes/<int:id>/delete', methods=['GET', 'POST'])
@login_required
@admin_required
def delete_attribute(id):
    """删除属性"""
    attribute = Attribute.query.get_or_404(id)
    
    # 获取上下文参数
    from_group = request.args.get('from_group', type=int)
    locked = request.args.get('locked', type=bool)

    # 检查是否有商品使用了这个属性
    if attribute.product_attributes.count() > 0:
        if request.method == 'GET':
            flash('该属性正在被商品使用，无法删除', 'error')
            # 构建返回URL，保持上下文
            return_params = {'group_id': attribute.group_id}
            if from_group:
                return_params['from_group'] = from_group
            if locked:
                return_params['locked'] = True
            return redirect(url_for('admin.attributes', **return_params))
        return jsonify({'success': False, 'message': '该属性正在被商品使用，无法删除'})

    try:
        group_id = attribute.group_id
        db.session.delete(attribute)
        db.session.commit()

        if request.method == 'GET':
            flash('属性删除成功', 'success')
            # 构建返回URL，保持上下文
            return_params = {'group_id': group_id}
            if from_group:
                return_params['from_group'] = from_group
            if locked:
                return_params['locked'] = True
            return redirect(url_for('admin.attributes', **return_params))
        return jsonify({'success': True, 'message': '属性删除成功'})
    except Exception as e:
        db.session.rollback()
        if request.method == 'GET':
            flash('删除失败，请重试', 'error')
            # 构建返回URL，保持上下文
            return_params = {'group_id': attribute.group_id}
            if from_group:
                return_params['from_group'] = from_group
            if locked:
                return_params['locked'] = True
            return redirect(url_for('admin.attributes', **return_params))
        return jsonify({'success': False, 'message': '删除失败，请重试'})

@admin_bp.route('/attributes/batch-delete', methods=['POST'])
@login_required
@admin_required
def batch_delete_attributes():
    """批量删除属性"""
    data = request.get_json()
    if not data or 'ids' not in data:
        return jsonify({'success': False, 'message': '请求数据无效'})

    ids = data['ids']
    if not ids:
        return jsonify({'success': False, 'message': '请选择要删除的属性'})

    try:
        # 查询要删除的属性
        attributes = Attribute.query.filter(Attribute.id.in_(ids)).all()

        if not attributes:
            return jsonify({'success': False, 'message': '未找到要删除的属性'})

        # 检查是否有属性正在被商品使用
        used_attributes = []
        for attribute in attributes:
            if attribute.product_attributes.count() > 0:
                used_attributes.append(f"{attribute.name}: {attribute.value}")

        if used_attributes:
            return jsonify({
                'success': False,
                'message': f'以下属性正在被商品使用，无法删除：{", ".join(used_attributes)}'
            })

        # 执行删除
        for attribute in attributes:
            db.session.delete(attribute)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功删除 {len(attributes)} 个属性'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '批量删除失败，请重试'})

# 商品管理
@admin_bp.route('/products')
@login_required
@admin_required
def products():
    """商品管理"""
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category_id', 0, type=int)
    search = request.args.get('search', '')

    # 构建查询
    query = Product.query

    # 分类筛选
    if category_id:
        category = Category.query.get(category_id)
        if category:
            # 获取该分类及其所有子分类的ID
            category_ids = [category_id]
            for child in category.get_all_children():
                category_ids.append(child.id)
            query = query.filter(Product.category_id.in_(category_ids))

    # 搜索筛选
    if search:
        query = query.filter(
            db.or_(
                Product.name.contains(search),
                Product.description.contains(search)
            )
        )

    # 分页查询
    products = query.order_by(Product.sort_order, Product.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # 获取分类树结构
    def build_category_tree(parent_id=None):
        categories = Category.query.filter_by(parent_id=parent_id, is_active=True)\
                                 .order_by(Category.sort_order, Category.name).all()
        tree = []
        for category in categories:
            children = build_category_tree(category.id)
            product_count = Product.query.filter_by(category_id=category.id).count()
            # 递归统计子分类的商品数量
            for child in category.get_all_children():
                product_count += Product.query.filter_by(category_id=child.id).count()
            
            tree.append({
                'category': category,
                'children': children,
                'product_count': product_count,
                'has_children': len(children) > 0
            })
        return tree

    category_tree = build_category_tree()

    return render_template('admin/products.html',
                         products=products,
                         category_tree=category_tree,
                         current_category_id=category_id,
                         search=search)

@admin_bp.route('/api/category-attributes/<int:category_id>')
@login_required
@admin_required
def get_category_attributes(category_id):
    """获取指定分类的属性列表（AJAX接口）"""
    try:
        attributes = []

        if category_id and category_id != 0:
            category = Category.query.get(category_id)
            if category:
                # 获取该分类的属性组（直接关系）
                bound_groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()
                for group in bound_groups:
                    group_attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()
                    if group_attributes:
                        for attr in group_attributes:
                            # 格式：大属性 - 小属性 (属性值)
                            label = f"{group.name} - {attr.name}"
                            if attr.value and attr.value != attr.name:
                                label += f" ({attr.value})"
                            if attr.price_modifier != 0:
                                if attr.price_modifier_type == 'percentage':
                                    label += f" [+{attr.price_modifier}%]"
                                else:
                                    label += f" [+¥{attr.price_modifier}]"
                            attributes.append({
                                'id': attr.id,
                                'label': label,
                                'group_name': group.name,
                                'attr_name': attr.name,
                                'value': attr.value,
                                'price_modifier': attr.price_modifier,
                                'price_modifier_type': attr.price_modifier_type
                            })

        return jsonify({
            'success': True,
            'attributes': attributes
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取属性失败: {str(e)}'
        }), 400

@admin_bp.route('/products/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_product():
    """添加商品"""
    # 获取预选的分类ID
    category_id = request.args.get('category_id', type=int)
    form = ProductForm(category_id=category_id)

    # 如果有预选的分类，设置默认值并更新属性选择项
    if category_id and request.method == 'GET':
        form.category_id.data = category_id
        # 重新调用update_attribute_choices以确保属性选择项正确加载
        form.update_attribute_choices(category_id)

    # 处理POST请求时，先更新属性选择项再验证
    if request.method == 'POST':
        submitted_category_id = form.category_id.data
        if submitted_category_id and submitted_category_id != 0:
            form.update_attribute_choices(submitted_category_id)

    if form.validate_on_submit():
        # 处理图片上传
        image_url = form.image_url.data

        # 如果有文件上传，优先使用上传的文件
        if form.image_file.data:
            success, message, file_path = save_uploaded_file(form.image_file.data, 'products')
            if success:
                image_url = file_path
            else:
                flash(f'图片上传失败: {message}', 'error')
                return render_template('admin/product_form.html', form=form, title='添加商品')

        product = Product(
            name=form.name.data,
            description=form.description.data,
            category_id=form.category_id.data,
            base_price=form.base_price.data,
            min_quantity=form.min_quantity.data,
            max_quantity=form.max_quantity.data,
            unit=form.unit.data,
            sort_order=form.sort_order.data,
            is_active=form.is_active.data,
            image_url=image_url
        )

        try:
            db.session.add(product)
            db.session.flush()  # 获取product.id

            # 处理属性绑定
            if form.selected_attributes.data:
                from models.order import ProductAttribute
                
                # 去重：确保每个属性ID只添加一次
                unique_attr_ids = list(set(form.selected_attributes.data))
                print(f"[调试] 表单原始属性IDs: {form.selected_attributes.data}")
                print(f"[调试] 去重后属性IDs: {unique_attr_ids}")
                
                for attr_id in unique_attr_ids:
                    try:
                        # 检查是否已经存在该绑定关系
                        existing = ProductAttribute.query.filter_by(
                            product_id=product.id,
                            attribute_id=attr_id
                        ).first()
                        
                        if existing:
                            print(f"[警告] 属性绑定已存在，跳过: 商品{product.id} -> 属性{attr_id}")
                            continue
                            
                        product_attr = ProductAttribute(
                            product_id=product.id,
                            attribute_id=attr_id
                        )
                        db.session.add(product_attr)
                        print(f"[调试] 添加属性绑定: 商品{product.id} -> 属性{attr_id}")
                    except Exception as attr_error:
                        print(f"[错误] 添加单个属性绑定失败: 属性{attr_id}, 错误: {str(attr_error)}")
                        # 不要因为单个属性失败就回滚整个事务，继续处理其他属性

            db.session.commit()
            flash('商品添加成功', 'success')
            return redirect(url_for('admin.products'))
        except Exception as e:
            db.session.rollback()
            import traceback
            current_app.logger.error(f"添加商品失败: {str(e)}")
            current_app.logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 输出调试信息到控制台
            print(f"添加商品失败的详细信息:")
            print(f"  - 商品名称: {form.name.data}")
            print(f"  - 分类ID: {form.category_id.data}")
            print(f"  - 基础价格: {form.base_price.data}")
            print(f"  - 选中属性: {form.selected_attributes.data}")
            print(f"  - 原始表单数据 selected_attributes: {request.form.getlist('selected_attributes')}")
            print(f"  - selected_attributes 数据类型: {type(form.selected_attributes.data)}")
            if form.selected_attributes.data:
                print(f"  - selected_attributes 去重前长度: {len(form.selected_attributes.data)}")
                print(f"  - selected_attributes 去重后长度: {len(set(form.selected_attributes.data))}")
                print(f"  - 是否有重复: {len(form.selected_attributes.data) != len(set(form.selected_attributes.data))}")
            print(f"  - 错误详情: {str(e)}")
            traceback.print_exc()
            flash(f'添加失败: {str(e)}', 'error')

    return render_template('admin/product_form.html', form=form, title='添加商品')

@admin_bp.route('/products/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_product(id):
    """编辑商品"""
    product = Product.query.get_or_404(id)
    # 传递当前商品的分类ID，以便正确加载属性
    form = ProductForm(obj=product, category_id=product.category_id)

    # 设置当前绑定的属性
    if request.method == 'GET':
        current_attributes = [pa.attribute_id for pa in product.product_attributes]
        form.selected_attributes.data = current_attributes
        print(f"[调试] 商品 {product.name} 当前绑定属性: {current_attributes}")

    if request.method == 'POST':
        # 调试信息：表单提交数据
        print(f"[调试] 表单提交数据:")
        print(f"  - 表单验证状态: {form.validate()}")
        print(f"  - selected_attributes 原始数据: {request.form.getlist('selected_attributes')}")
        print(f"  - form.selected_attributes.data: {form.selected_attributes.data}")
        print(f"  - 表单错误: {form.errors}")

    if form.validate_on_submit():
        old_data = {
            'name': product.name,
            'base_price': float(product.base_price),
            'is_active': product.is_active
        }

        # 处理图片删除
        if request.form.get('delete_current_image') == '1':
            if product.image_url and not product.image_url.startswith('https://via.placeholder.com'):
                delete_file(product.image_url)
                product.image_url = None

        # 处理新图片上传
        image_url = product.image_url  # 保持原有图片

        # 如果有文件上传，优先使用上传的文件
        if form.image_file.data:
            success, message, file_path = save_uploaded_file(form.image_file.data, 'products')
            if success:
                # 删除旧图片
                if product.image_url and not product.image_url.startswith('https://via.placeholder.com'):
                    delete_file(product.image_url)
                image_url = file_path
            else:
                flash(f'图片上传失败: {message}', 'error')
                return render_template('admin/product_form.html', form=form, product=product, title='编辑商品')
        # 如果没有文件上传但有URL输入，使用URL
        elif form.image_url.data and form.image_url.data != product.image_url:
            # 删除旧的上传图片（如果是上传的）
            if product.image_url and product.image_url.startswith('/static/uploads/'):
                delete_file(product.image_url)
            image_url = form.image_url.data

        product.name = form.name.data
        product.description = form.description.data
        product.category_id = form.category_id.data if form.category_id.data != 0 else None
        product.base_price = form.base_price.data
        product.min_quantity = form.min_quantity.data
        product.max_quantity = form.max_quantity.data
        product.unit = form.unit.data
        product.image_url = image_url
        product.is_active = form.is_active.data
        product.sort_order = form.sort_order.data

        # 更新属性绑定 - 使用增量更新而非全量替换
        from models.order import ProductAttribute
        
        # 获取当前绑定的属性ID
        current_bindings = ProductAttribute.query.filter_by(product_id=product.id).all()
        current_attr_ids = set(binding.attribute_id for binding in current_bindings)
        
        # 获取新提交的属性ID，去除重复
        new_attr_ids = set(form.selected_attributes.data or [])
        
        print(f"[调试] 当前绑定属性: {sorted(current_attr_ids)}")
        print(f"[调试] 新提交属性: {sorted(new_attr_ids)}")
        
        # 计算需要删除和添加的属性
        attrs_to_remove = current_attr_ids - new_attr_ids  # 当前有但新提交中没有的
        attrs_to_add = new_attr_ids - current_attr_ids     # 新提交中有但当前没有的
        
        print(f"[调试] 需要删除的属性: {sorted(attrs_to_remove)}")
        print(f"[调试] 需要添加的属性: {sorted(attrs_to_add)}")
        
        # 删除不再需要的属性绑定
        if attrs_to_remove:
            for attr_id in attrs_to_remove:
                ProductAttribute.query.filter_by(
                    product_id=product.id, 
                    attribute_id=attr_id
                ).delete()
                print(f"[调试] 删除属性绑定: 商品{product.id} -> 属性{attr_id}")
        
        # 添加新的属性绑定
        if attrs_to_add:
            for attr_id in attrs_to_add:
                try:
                    product_attr = ProductAttribute(
                        product_id=product.id,
                        attribute_id=attr_id
                    )
                    db.session.add(product_attr)
                    print(f"[调试] 添加属性绑定: 商品{product.id} -> 属性{attr_id}")
                except Exception as e:
                    print(f"[错误] 添加属性绑定失败: {e}")

        try:
            db.session.commit()
            print(f"[调试] 增量更新完成")

            # 验证最终结果
            final_bindings = ProductAttribute.query.filter_by(product_id=product.id).all()
            final_attr_ids = [binding.attribute_id for binding in final_bindings]
            print(f"[调试] 最终绑定属性: {sorted(final_attr_ids)}")

            change_summary = []
            if attrs_to_remove:
                change_summary.append(f"删除了{len(attrs_to_remove)}个属性")
            if attrs_to_add:
                change_summary.append(f"添加了{len(attrs_to_add)}个属性")
            if not attrs_to_remove and not attrs_to_add:
                change_summary.append("属性绑定无变化")
            
            flash(f'商品《{product.name}》更新成功！{", ".join(change_summary)}，当前共绑定{len(final_attr_ids)}个属性。', 'success')
            return redirect(url_for('admin.products'))
        except Exception as e:
            db.session.rollback()
            print(f"[错误] 数据库提交失败: {str(e)}")
            import traceback
            traceback.print_exc()
            flash(f'更新失败: {str(e)}', 'error')
    else:
        # 表单验证失败时的处理
        if request.method == 'POST':
            print(f"[调试] 表单验证失败，错误详情: {form.errors}")
            if form.errors:
                for field, errors in form.errors.items():
                    for error in errors:
                        flash(f'{field}: {error}', 'error')

    return render_template('admin/product_form.html', form=form, product=product, title='编辑商品')

@admin_bp.route('/api/attribute-groups')
@login_required
@admin_required
def get_attribute_groups():
    """获取所有属性组（AJAX接口）"""
    try:
        category_id = request.args.get('category_id', type=int)

        query = AttributeGroup.query.filter_by(is_active=True)
        if category_id:
            query = query.filter_by(category_id=category_id)

        groups = query.order_by(AttributeGroup.sort_order, AttributeGroup.name).all()

        groups_data = []
        for group in groups:
            # 获取该组下的属性
            attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()

            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'display_type': group.display_type,
                'category_id': group.category_id,
                'category_name': group.category.name if group.category else '未分类',
                'sort_order': group.sort_order,
                'is_active': group.is_active,
                'attributes': []
            }

            for attr in attributes:
                group_data['attributes'].append({
                    'id': attr.id,
                    'name': attr.name,
                    'value': attr.value,
                    'price_modifier': float(attr.price_modifier) if attr.price_modifier else 0,
                    'price_modifier_type': attr.price_modifier_type,
                    'sort_order': attr.sort_order,
                    'is_active': attr.is_active
                })

            groups_data.append(group_data)

        return jsonify({
            'success': True,
            'groups': groups_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取属性组失败: {str(e)}'
        }), 500

@admin_bp.route('/api/attributes-by-category/<int:category_id>')
@login_required
@admin_required
def get_attributes_by_category(category_id):
    """根据分类ID获取属性列表（AJAX接口）"""
    try:
        category = Category.query.get_or_404(category_id)

        attributes_data = []
        # 获取该分类的属性组（直接关系）
        bound_groups = category.attribute_groups.filter_by(is_active=True).order_by(AttributeGroup.sort_order, AttributeGroup.name).all()

        for group in bound_groups:
            attributes = Attribute.query.filter_by(group_id=group.id, is_active=True).order_by(Attribute.sort_order, Attribute.name).all()
            if attributes:
                group_data = {
                    'group_name': group.name,
                    'group_id': group.id,
                    'attributes': []
                }

                for attr in attributes:
                    # 格式：大属性 - 小属性 (属性值)
                    label = f"{group.name} - {attr.name}"
                    if attr.value and attr.value != attr.name:
                        label += f" ({attr.value})"
                    if attr.price_modifier != 0:
                        if attr.price_modifier_type == 'percentage':
                            label += f" [+{attr.price_modifier}%]"
                        else:
                            label += f" [+¥{attr.price_modifier}]"

                    group_data['attributes'].append({
                        'id': attr.id,
                        'name': attr.name,
                        'value': attr.value,
                        'label': label,
                        'price_modifier': float(attr.price_modifier),
                        'price_modifier_type': attr.price_modifier_type
                    })

                attributes_data.append(group_data)

        return jsonify({
            'success': True,
            'attributes': attributes_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取属性失败: {str(e)}'
        }), 500

@admin_bp.route('/products/<int:id>/delete', methods=['GET', 'POST'])
@login_required
@admin_required
def delete_product(id):
    """删除商品"""
    product = Product.query.get_or_404(id)
    
    if request.method == 'GET':
        # GET请求时，显示错误消息并重定向
        flash('删除操作必须通过确认对话框进行，请在商品列表页面点击删除按钮。', 'error')
        return redirect(url_for('admin.products'))
    
    try:
        # 记录请求信息用于调试
        print(f"删除商品请求 - ID: {id}, 商品名: {product.name}")
        print(f"请求方法: {request.method}")
        print(f"Content-Type: {request.content_type}")
        print(f"请求数据: {request.form}")

        # 检查是否有订单项
        if product.order_items.count() > 0:
            return jsonify({'success': False, 'message': '该商品已有订单记录，无法删除'})

        # 删除商品图片文件
        if product.image_url and not product.image_url.startswith('https://via.placeholder.com'):
            try:
                delete_file(product.image_url)
            except Exception as e:
                print(f"删除图片文件失败: {str(e)}")

        # 删除商品
        db.session.delete(product)
        db.session.commit()

        print(f"商品删除成功: {product.name}")
        return jsonify({'success': True, 'message': '商品删除成功'})

    except Exception as e:
        db.session.rollback()
        print(f"删除商品失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'删除失败：{str(e)}'}), 500

# 订单管理
@admin_bp.route('/orders')
@login_required
@admin_required
def orders():
    """订单管理"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    search = request.args.get('search', '')
    
    # 构建查询
    query = Order.query
    
    # 状态筛选
    if status:
        query = query.filter(Order.status == status)
    
    # 搜索筛选
    if search:
        query = query.join(User).filter(
            db.or_(
                Order.order_no.contains(search),
                User.username.contains(search),
                User.real_name.contains(search),
                User.email.contains(search)
            )
        )
    
    # 分页查询
    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # 统计数据
    stats = {
        'total': Order.query.count(),
        'pending': Order.query.filter_by(status='pending').count(),
        'paid': Order.query.filter_by(status='paid').count(),
        'processing': Order.query.filter_by(status='processing').count(),
        'shipped': Order.query.filter_by(status='shipped').count(),
        'delivered': Order.query.filter_by(status='delivered').count(),
        'cancelled': Order.query.filter_by(status='cancelled').count(),
    }
    
    return render_template('admin/orders.html', 
                         orders=orders, 
                         stats=stats,
                         current_status=status,
                         search=search)

@admin_bp.route('/orders/<int:id>')
@login_required
@admin_required
def order_detail(id):
    """订单详情"""
    order = Order.query.get_or_404(id)
    return render_template('admin/order_detail.html', order=order)

@admin_bp.route('/orders/<int:id>/print')
@login_required
@admin_required
def print_order(id):
    """打印订单"""
    order = Order.query.get_or_404(id)
    return render_template('admin/order_print.html', order=order)

@admin_bp.route('/orders/<int:id>/print-production')
@login_required
@admin_required
def print_production_order(id):
    """打印生产单"""
    order = Order.query.get_or_404(id)

    # 计算预期交付日期（订单创建后7天）
    expected_delivery_date = None
    if order.created_at:
        expected_delivery_date = order.created_at + timedelta(days=7)

    return render_template('admin/production_print.html',
                         order=order,
                         expected_delivery_date=expected_delivery_date)

@admin_bp.route('/custom-attributes-report')
@login_required
@admin_required
def custom_attributes_report():
    """自定义属性使用报告"""
    from models.order import OrderItem
    from sqlalchemy import func, text

    # 获取包含自定义属性的订单项
    custom_orders = db.session.query(OrderItem).filter(
        OrderItem.product_attributes.like('%自定义%')
    ).order_by(OrderItem.created_at.desc()).limit(100).all()

    # 统计自定义属性使用情况
    stats = {
        'total_orders_with_custom': len(custom_orders),
        'total_orders': OrderItem.query.count(),
        'custom_percentage': round(len(custom_orders) / max(OrderItem.query.count(), 1) * 100, 2)
    }

    return render_template('admin/custom_attributes_report.html',
                         custom_orders=custom_orders,
                         stats=stats)

@admin_bp.route('/api/custom-attributes-stats')
@login_required
@admin_required
def custom_attributes_stats():
    """获取自定义属性统计数据API"""
    from models.order import OrderItem
    from sqlalchemy import func

    try:
        # 总订单项数量
        total_items = OrderItem.query.count()

        # 包含自定义属性的订单项数量
        custom_items = db.session.query(OrderItem).filter(
            OrderItem.product_attributes.like('%自定义%')
        ).count()

        # 最近7天的自定义属性使用情况
        from datetime import datetime, timedelta
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        recent_custom_items = db.session.query(OrderItem).filter(
            OrderItem.product_attributes.like('%自定义%'),
            OrderItem.created_at >= seven_days_ago
        ).count()

        # 按属性组统计自定义使用情况
        custom_groups_stats = {}
        custom_items_list = db.session.query(OrderItem).filter(
            OrderItem.product_attributes.like('%自定义%')
        ).all()

        for item in custom_items_list:
            attr_details = item.get_attributes_detail()
            for attr in attr_details:
                if attr['is_custom']:
                    group_name = attr['name']
                    if group_name not in custom_groups_stats:
                        custom_groups_stats[group_name] = 0
                    custom_groups_stats[group_name] += 1

        return jsonify({
            'success': True,
            'data': {
                'total_items': total_items,
                'custom_items': custom_items,
                'custom_percentage': round(custom_items / max(total_items, 1) * 100, 2),
                'recent_custom_items': recent_custom_items,
                'custom_groups_stats': custom_groups_stats
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        }), 500

@admin_bp.route('/orders/<int:id>/print-shipping')
@login_required
@admin_required
def print_shipping_order(id):
    """打印发货单"""
    order = Order.query.get_or_404(id)
    
    # 计算预期交付日期（订单创建后3天）
    expected_delivery_date = None
    if order.created_at:
        expected_delivery_date = order.created_at + timedelta(days=3)
    
    return render_template('admin/shipping_print.html', 
                         order=order, 
                         expected_delivery_date=expected_delivery_date)

@admin_bp.route('/orders/<int:id>/update-status', methods=['POST'])
@login_required
@admin_required
def update_order_status(id):
    """更新订单状态"""
    from flask import current_app
    
    order = Order.query.get_or_404(id)
    data = request.get_json()
    
    current_app.logger.info(f"尝试更新订单状态 - 订单ID: {id}, 数据: {data}")
    
    if 'status' not in data:
        current_app.logger.error(f"订单状态更新失败 - 订单ID: {id}, 错误: 状态参数缺失")
        return jsonify({'success': False, 'message': '状态参数缺失'})
    
    new_status = data['status']
    valid_statuses = ['pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled']
    
    if new_status not in valid_statuses:
        current_app.logger.error(f"订单状态更新失败 - 订单ID: {id}, 错误: 无效状态值 {new_status}")
        return jsonify({'success': False, 'message': '无效的状态值'})
    
    try:
        old_status = order.status
        order.status = new_status
        
        db.session.commit()
        
        current_app.logger.info(f"订单状态更新成功 - 订单ID: {id}, {old_status} -> {new_status}")
        
        return jsonify({
            'success': True,
            'message': f'订单状态已更新为: {order.get_status_display()}'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"订单状态更新失败 - 订单ID: {id}, 错误: {str(e)}")
        return jsonify({'success': False, 'message': f'状态更新失败: {str(e)}'})

# 用户管理
@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    is_active = request.args.get('is_active', '')
    is_admin = request.args.get('is_admin', '')
    
    # 构建查询
    query = User.query
    
    # 搜索筛选
    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.real_name.contains(search),
                User.email.contains(search),
                User.phone.contains(search)
            )
        )
    
    # 状态筛选
    if is_active == '1':
        query = query.filter(User.is_active == True)
    elif is_active == '0':
        query = query.filter(User.is_active == False)
        
    if is_admin == '1':
        query = query.filter(User.is_admin == True)
    elif is_admin == '0':
        query = query.filter(User.is_admin == False)
    
    # 分页查询
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # 统计数据
    stats = {
        'total': User.query.count(),
        'active': User.query.filter_by(is_active=True).count(),
        'inactive': User.query.filter_by(is_active=False).count(),
        'admin': User.query.filter_by(is_admin=True).count(),
        'verified_email': User.query.filter_by(email_verified=True).count(),
        'verified_phone': User.query.filter_by(phone_verified=True).count(),
    }
    
    return render_template('admin/users.html', 
                         users=users, 
                         stats=stats,
                         search=search,
                         is_active=is_active,
                         is_admin=is_admin)

@admin_bp.route('/users/<int:id>')
@login_required
@admin_required
def user_detail(id):
    """用户详情"""
    user = User.query.get_or_404(id)
    
    # 获取用户的订单统计
    order_stats = {
        'total_orders': user.orders.count(),
        'total_amount': db.session.query(db.func.sum(Order.final_amount)).filter_by(user_id=user.id).scalar() or 0,
        'pending_orders': user.orders.filter_by(status='pending').count(),
        'completed_orders': user.orders.filter_by(status='delivered').count(),
    }
    
    # 获取最近订单
    recent_orders = user.orders.order_by(Order.created_at.desc()).limit(5).all()
    
    return render_template('admin/user_detail.html', 
                         user=user, 
                         order_stats=order_stats,
                         recent_orders=recent_orders)

@admin_bp.route('/users/<int:id>/update-status', methods=['POST'])
@login_required
@admin_required
def update_user_status(id):
    """更新用户状态"""
    user = User.query.get_or_404(id)
    data = request.get_json()
    
    try:
        old_data = {
            'is_active': user.is_active,
            'is_admin': user.is_admin
        }
        
        if 'is_active' in data:
            user.is_active = bool(data['is_active'])
        
        if 'is_admin' in data:
            user.is_admin = bool(data['is_admin'])
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '用户状态更新成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '状态更新失败，请重试'})

# 系统设置
@admin_bp.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    """系统设置"""
    # 获取分组配置
    grouped_configs = SystemConfig.get_configs_by_group()

    # 定义分组的显示顺序和标题
    group_order = [
        ('基本信息', 'fas fa-info-circle', 'primary'),
        ('运费设置', 'fas fa-truck', 'success'),
    ]

    # 确保所有定义的分组都存在
    for group_name, _, _ in group_order:
        if group_name not in grouped_configs:
            grouped_configs[group_name] = []

    return render_template('admin/settings.html',
                         grouped_configs=grouped_configs,
                         group_order=group_order)

# API：更新单个配置项
@admin_bp.route('/api/settings/<config_key>', methods=['POST'])
@login_required
@admin_required
def update_setting(config_key):
    """更新单个配置项"""
    try:
        data = request.get_json()
        if not data or 'value' not in data:
            return jsonify({'success': False, 'message': '缺少配置值'}), 400

        # 获取配置项
        config = SystemConfig.query.filter_by(config_key=config_key).first()
        if not config:
            return jsonify({'success': False, 'message': '配置项不存在'}), 404

        # 验证配置值
        new_value = data['value']
        is_valid, error_msg = config.validate_value(new_value)
        if not is_valid:
            return jsonify({'success': False, 'message': error_msg}), 400

        # 记录旧值
        old_value = config.config_value

        # 更新配置值
        config.config_value = str(new_value)
        config.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '配置已更新',
            'value': config.config_value
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

# API：导出配置
@admin_bp.route('/api/settings/export', methods=['GET'])
@login_required
@admin_required
def export_settings():
    """导出系统配置"""
    try:
        configs = SystemConfig.query.all()
        export_data = []

        for config in configs:
            export_data.append({
                'config_key': config.config_key,
                'config_value': config.config_value,
                'description': config.description,
                'group_name': config.group_name,
                'config_type': config.config_type,
                'is_required': config.is_required,
                'validation_rules': config.validation_rules
            })

        response = jsonify({
            'success': True,
            'data': export_data,
            'export_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S'),
            'total_count': len(export_data)
        })

        # 设置下载文件名
        filename = f"system_configs_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'

        return response

    except Exception as e:
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

@admin_bp.route('/quantity-discounts/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_quantity_discount(id):
    """编辑数量折扣"""
    discount = QuantityDiscount.query.get_or_404(id)
    form = QuantityDiscountForm(obj=discount)

    if form.validate_on_submit():
        # 检查是否有重叠的数量区间（排除当前记录）
        existing = QuantityDiscount.query.filter(
            QuantityDiscount.product_id == form.product_id.data,
            QuantityDiscount.id != id,
            QuantityDiscount.is_active == True
        ).all()

        for existing_discount in existing:
            if _quantity_ranges_overlap(
                form.min_quantity.data,
                form.max_quantity.data or 999999,
                existing_discount.min_quantity,
                existing_discount.max_quantity or 999999
            ):
                flash('数量区间与现有折扣规则重叠', 'error')
                return render_template('admin/quantity_discount_form.html', form=form, discount=discount, title='编辑数量折扣')

        old_data = {
            'product_id': discount.product_id,
            'min_quantity': discount.min_quantity,
            'max_quantity': discount.max_quantity,
            'discount_value': float(discount.discount_value),
            'is_active': discount.is_active
        }

        discount.product_id = form.product_id.data
        discount.min_quantity = form.min_quantity.data
        discount.max_quantity = form.max_quantity.data
        discount.discount_type = form.discount_type.data
        discount.discount_value = form.discount_value.data
        discount.is_active = form.is_active.data

        try:
            db.session.commit()

            flash('数量折扣更新成功', 'success')
            return redirect(url_for('admin.products'))
        except Exception as e:
            db.session.rollback()
            flash('更新失败，请重试', 'error')

    return render_template('admin/quantity_discount_form.html', form=form, discount=discount, title='编辑数量折扣')

@admin_bp.route('/quantity-discounts/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_quantity_discount(id):
    """删除数量折扣"""
    discount = QuantityDiscount.query.get_or_404(id)

    try:
        db.session.delete(discount)
        db.session.commit()

        return jsonify({'success': True, 'message': '数量折扣删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '删除失败，请重试'})

def _quantity_ranges_overlap(min1, max1, min2, max2):
    """检查两个数量区间是否重叠"""
    return not (max1 < min2 or max2 < min1)

@admin_bp.route('/test-delete')
@login_required
@admin_required
def test_delete():
    """测试删除功能页面"""
    return render_template('admin/test_delete.html')
