#!/usr/bin/env python3
"""
测试模型字段是否正确加载
"""

import os
import sys
sys.path.append('.')

# 设置环境变量
os.environ['FLASK_ENV'] = 'development'

from app import create_app
from models.product import AttributeGroup
from models import db

def test_model_fields():
    """测试AttributeGroup模型的字段"""

    app = create_app()
    with app.app_context():
        try:
            # 检查模型的字段
            print("AttributeGroup 模型的字段:")
            for column in AttributeGroup.__table__.columns:
                print(f"  - {column.name}: {column.type}")
            
            print("\n检查新字段是否存在:")
            
            # 检查 is_multiple 字段
            if hasattr(AttributeGroup, 'is_multiple'):
                print("✓ is_multiple 字段存在于模型中")
                print(f"  类型: {AttributeGroup.is_multiple.type}")
                print(f"  默认值: {AttributeGroup.is_multiple.default}")
            else:
                print("❌ is_multiple 字段不存在于模型中")
            
            # 检查 max_selections 字段
            if hasattr(AttributeGroup, 'max_selections'):
                print("✓ max_selections 字段存在于模型中")
                print(f"  类型: {AttributeGroup.max_selections.type}")
                print(f"  默认值: {AttributeGroup.max_selections.default}")
            else:
                print("❌ max_selections 字段不存在于模型中")
            
            # 尝试创建一个测试对象
            print("\n尝试创建测试对象:")
            test_data = {
                'name': '测试属性组',
                'description': '测试描述',
                'display_type': 'radio',
                'sort_order': 0,
                'is_active': True,
                'is_multiple': True,
                'max_selections': 3
            }
            
            test_group = AttributeGroup(**test_data)
            print("✓ 成功创建 AttributeGroup 对象")
            print(f"  is_multiple: {test_group.is_multiple}")
            print(f"  max_selections: {test_group.max_selections}")
            
            # 不实际保存到数据库，只是测试对象创建
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_model_fields()
