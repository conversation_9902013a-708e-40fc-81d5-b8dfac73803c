#!/usr/bin/env python3
"""
修复 display_type 字段的 ENUM 类型
"""

import pymysql
import os

def fix_display_type_enum():
    """修复 display_type 字段的 ENUM 类型"""
    
    # 数据库连接配置
    config = {
        'host': os.environ.get('MYSQL_HOST', 'localhost'),
        'user': os.environ.get('MYSQL_USER', 'root'),
        'password': os.environ.get('MYSQL_PASSWORD', '123456'),
        'database': os.environ.get('MYSQL_DB', 'print_shop'),
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print('已连接到数据库')
        
        # 检查当前的 display_type 字段定义
        cursor.execute("""
            SELECT COLUMN_TYPE 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'attribute_groups' 
            AND column_name = 'display_type'
        """, (config['database'],))
        
        result = cursor.fetchone()
        if result:
            print(f'当前 display_type 字段类型: {result[0]}')
        else:
            print('未找到 display_type 字段')
            return
        
        # 修改 display_type 字段，确保包含所有需要的选项
        print('正在修改 display_type 字段...')
        cursor.execute("""
            ALTER TABLE attribute_groups 
            MODIFY COLUMN display_type ENUM('radio', 'select', 'checkbox') 
            DEFAULT 'radio' NOT NULL
        """)
        
        print('✓ display_type 字段修改成功')
        
        # 验证修改结果
        cursor.execute("""
            SELECT COLUMN_TYPE 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'attribute_groups' 
            AND column_name = 'display_type'
        """, (config['database'],))
        
        result = cursor.fetchone()
        print(f'修改后 display_type 字段类型: {result[0]}')
        
        # 提交更改
        connection.commit()
        print('✅ 修复完成！')
        
    except Exception as e:
        print(f'❌ 操作失败: {e}')
        if 'connection' in locals():
            connection.rollback()
        raise
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
        print('数据库连接已关闭')

if __name__ == '__main__':
    fix_display_type_enum()
